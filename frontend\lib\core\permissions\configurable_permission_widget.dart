import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';
import 'permission_config_service.dart';

/// Provider for permission config service
final permissionConfigServiceProvider = Provider<PermissionConfigService>((ref) {
  return PermissionConfigService();
});

/// A widget that checks permissions based on configurable screen/widget permissions
class ConfigurablePermissionWidget extends ConsumerWidget {
  final String screenName;
  final String? widgetName;
  final Widget child;
  final Widget? fallback;
  final bool showFallbackOnNoPermission;

  const ConfigurablePermissionWidget({
    super.key,
    required this.screenName,
    this.widgetName,
    required this.child,
    this.fallback,
    this.showFallbackOnNoPermission = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authState = ref.watch(authStateProvider);
    final permissionService = ref.read(permissionConfigServiceProvider);

    // If not authenticated, don't show anything
    if (!authState.isAuthenticated || authState.user == null) {
      return showFallbackOnNoPermission
          ? (fallback ?? const SizedBox.shrink())
          : const SizedBox.shrink();
    }

    final user = authState.user!;
    final userPermissions = user.permissions;
    final userRoles = user.roles;

    bool hasPermission;

    if (widgetName != null) {
      // Check widget permission
      hasPermission = permissionService.hasWidgetPermission(
        screenName,
        widgetName!,
        userPermissions,
        userRoles,
      );
    } else {
      // Check screen permission
      hasPermission = permissionService.hasScreenPermission(
        screenName,
        userPermissions,
        userRoles,
      );
    }

    if (hasPermission) {
      return child;
    } else {
      return showFallbackOnNoPermission
          ? (fallback ?? const SizedBox.shrink())
          : const SizedBox.shrink();
    }
  }
}

/// A widget that wraps an entire screen with permission checking
class ConfigurablePermissionScreen extends ConsumerWidget {
  final String screenName;
  final Widget child;
  final Widget? fallback;

  const ConfigurablePermissionScreen({
    super.key,
    required this.screenName,
    required this.child,
    this.fallback,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      showFallbackOnNoPermission: true,
      fallback: fallback ?? _buildNoPermissionScreen(context),
      child: child,
    );
  }

  Widget _buildNoPermissionScreen(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Access Denied'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              'Access Denied',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'You don\'t have permission to access this screen.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// A floating action button that respects widget permissions
class ConfigurablePermissionFAB extends ConsumerWidget {
  final String screenName;
  final String widgetName;
  final VoidCallback? onPressed;
  final Widget? child;
  final String? tooltip;
  final Color? backgroundColor;

  const ConfigurablePermissionFAB({
    super.key,
    required this.screenName,
    required this.widgetName,
    this.onPressed,
    this.child,
    this.tooltip,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      child: FloatingActionButton(
        onPressed: onPressed,
        tooltip: tooltip,
        backgroundColor: backgroundColor,
        child: child,
      ),
    );
  }
}

/// A list tile that respects widget permissions
class ConfigurablePermissionListTile extends ConsumerWidget {
  final String screenName;
  final String widgetName;
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;

  const ConfigurablePermissionListTile({
    super.key,
    required this.screenName,
    required this.widgetName,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      child: ListTile(
        leading: leading,
        title: title,
        subtitle: subtitle,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }
}

/// A button that respects widget permissions
class ConfigurablePermissionButton extends ConsumerWidget {
  final String screenName;
  final String widgetName;
  final VoidCallback? onPressed;
  final Widget child;
  final ButtonStyle? style;

  const ConfigurablePermissionButton({
    super.key,
    required this.screenName,
    required this.widgetName,
    this.onPressed,
    required this.child,
    this.style,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      child: ElevatedButton(
        onPressed: onPressed,
        style: style,
        child: child,
      ),
    );
  }
}

/// A card that respects widget permissions
class ConfigurablePermissionCard extends ConsumerWidget {
  final String screenName;
  final String widgetName;
  final Widget child;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;

  const ConfigurablePermissionCard({
    super.key,
    required this.screenName,
    required this.widgetName,
    required this.child,
    this.margin,
    this.color,
    this.elevation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      child: Card(
        margin: margin,
        color: color,
        elevation: elevation,
        child: child,
      ),
    );
  }
}

/// A section that can be shown/hidden based on permissions
class ConfigurablePermissionSection extends ConsumerWidget {
  final String screenName;
  final String widgetName;
  final String title;
  final List<Widget> children;
  final bool showTitle;

  const ConfigurablePermissionSection({
    super.key,
    required this.screenName,
    required this.widgetName,
    required this.title,
    required this.children,
    this.showTitle = true,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (showTitle) ...[
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                title,
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
          ...children,
        ],
      ),
    );
  }
}

/// Extension to easily add permission checking to existing widgets
extension ConfigurablePermissionExtension on Widget {
  Widget withPermission({
    required String screenName,
    String? widgetName,
    Widget? fallback,
    bool showFallbackOnNoPermission = false,
  }) {
    return ConfigurablePermissionWidget(
      screenName: screenName,
      widgetName: widgetName,
      fallback: fallback,
      showFallbackOnNoPermission: showFallbackOnNoPermission,
      child: this,
    );
  }
}
