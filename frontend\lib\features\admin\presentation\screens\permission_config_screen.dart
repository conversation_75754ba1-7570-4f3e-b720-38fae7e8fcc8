import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../../../../core/permissions/permission_config_service.dart';
import '../../../../core/permissions/screen_permission_config.dart';
import '../../../../core/utils/app_utils.dart';
import '../widgets/permission_config_card.dart';
import '../widgets/edit_permission_dialog.dart';

final permissionConfigServiceProvider = Provider<PermissionConfigService>((ref) {
  return PermissionConfigService();
});

final permissionConfigsProvider = FutureProvider<Map<String, ScreenPermissionConfig>>((ref) async {
  final service = ref.read(permissionConfigServiceProvider);
  await service.initialize();
  return service.getAllConfigs();
});

class PermissionConfigScreen extends ConsumerStatefulWidget {
  const PermissionConfigScreen({super.key});

  @override
  ConsumerState<PermissionConfigScreen> createState() => _PermissionConfigScreenState();
}

class _PermissionConfigScreenState extends ConsumerState<PermissionConfigScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Permission Configuration'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Screen Permissions', icon: Icon(Icons.web)),
            Tab(text: 'Widget Permissions', icon: Icon(Icons.widgets)),
          ],
        ),
        actions: [
          DynamicRoleBasedWidget(
            requiredPermissions: const ['permissions.manage'],
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.invalidate(permissionConfigsProvider);
              },
              tooltip: 'Refresh',
            ),
          ),
          DynamicRoleBasedWidget(
            requiredPermissions: const ['permissions.manage'],
            child: PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'export':
                    _exportConfigs();
                    break;
                  case 'import':
                    _importConfigs();
                    break;
                  case 'reset':
                    _resetToDefaults();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'export',
                  child: ListTile(
                    leading: Icon(Icons.download),
                    title: Text('Export Configs'),
                    dense: true,
                  ),
                ),
                const PopupMenuItem(
                  value: 'import',
                  child: ListTile(
                    leading: Icon(Icons.upload),
                    title: Text('Import Configs'),
                    dense: true,
                  ),
                ),
                const PopupMenuDivider(),
                const PopupMenuItem(
                  value: 'reset',
                  child: ListTile(
                    leading: Icon(Icons.restore, color: Colors.red),
                    title: Text('Reset to Defaults', style: TextStyle(color: Colors.red)),
                    dense: true,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      body: DynamicRoleBasedWidget(
        requiredPermissions: const ['permissions.read'],
        fallback: _buildNoPermissionView(),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildScreenPermissionsTab(),
            _buildWidgetPermissionsTab(),
          ],
        ),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to manage permissions.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildScreenPermissionsTab() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildScreenPermissionsList(),
        ),
      ],
    );
  }

  Widget _buildWidgetPermissionsTab() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: _buildWidgetPermissionsList(),
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: _tabController.index == 0 ? 'Search screens...' : 'Search widgets...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }

  Widget _buildScreenPermissionsList() {
    final configsAsync = ref.watch(permissionConfigsProvider);

    return configsAsync.when(
      data: (configs) {
        final filteredConfigs = _searchQuery.isEmpty
            ? configs
            : Map.fromEntries(
                configs.entries.where((entry) =>
                    entry.key.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                    entry.value.screenName.toLowerCase().contains(_searchQuery.toLowerCase())),
              );

        if (filteredConfigs.isEmpty) {
          return _buildEmptyState(
            'No screen configurations found',
            'Create your first screen permission configuration.',
            Icons.web,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(permissionConfigsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredConfigs.length,
            itemBuilder: (context, index) {
              final entry = filteredConfigs.entries.elementAt(index);
              final screenName = entry.key;
              final config = entry.value;

              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: PermissionConfigCard(
                  title: config.screenName,
                  subtitle: '${config.requiredPermissions.length} permissions, ${config.allowedRoles.length} roles',
                  isEnabled: config.isEnabled,
                  permissions: config.requiredPermissions,
                  roles: config.allowedRoles,
                  onEdit: () => _editScreenConfig(config),
                  onToggle: () => _toggleScreenConfig(config),
                  onDelete: () => _deleteScreenConfig(screenName),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorView('Error loading configurations', error.toString()),
    );
  }

  Widget _buildWidgetPermissionsList() {
    final configsAsync = ref.watch(permissionConfigsProvider);

    return configsAsync.when(
      data: (configs) {
        final allWidgets = <String, WidgetPermissionConfig>{};
        
        for (final screenConfig in configs.values) {
          for (final entry in screenConfig.widgetPermissions.entries) {
            final key = '${screenConfig.screenName}.${entry.key}';
            allWidgets[key] = entry.value;
          }
        }

        final filteredWidgets = _searchQuery.isEmpty
            ? allWidgets
            : Map.fromEntries(
                allWidgets.entries.where((entry) =>
                    entry.key.toLowerCase().contains(_searchQuery.toLowerCase()) ||
                    entry.value.widgetName.toLowerCase().contains(_searchQuery.toLowerCase())),
              );

        if (filteredWidgets.isEmpty) {
          return _buildEmptyState(
            'No widget configurations found',
            'Widget permissions are configured within screen configurations.',
            Icons.widgets,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(permissionConfigsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            itemCount: filteredWidgets.length,
            itemBuilder: (context, index) {
              final entry = filteredWidgets.entries.elementAt(index);
              final key = entry.key;
              final config = entry.value;
              final screenName = key.split('.').first;

              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: PermissionConfigCard(
                  title: config.widgetName,
                  subtitle: 'Screen: $screenName • ${config.requiredPermissions.length} permissions',
                  isEnabled: config.isEnabled && config.isVisible,
                  permissions: config.requiredPermissions,
                  roles: config.allowedRoles,
                  onEdit: () => _editWidgetConfig(screenName, config),
                  onToggle: () => _toggleWidgetConfig(screenName, config),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorView('Error loading configurations', error.toString()),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String title, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => ref.invalidate(permissionConfigsProvider),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  void _editScreenConfig(ScreenPermissionConfig config) {
    showDialog(
      context: context,
      builder: (context) => EditPermissionDialog(
        screenConfig: config,
        onSave: (updatedConfig) async {
          final service = ref.read(permissionConfigServiceProvider);
          final success = await service.updateScreenConfig(updatedConfig);
          
          if (success && mounted) {
            ref.invalidate(permissionConfigsProvider);
            AppUtils.showSuccessSnackBar(context, 'Screen configuration updated');
          } else if (mounted) {
            AppUtils.showErrorSnackBar(context, 'Failed to update configuration');
          }
        },
      ),
    );
  }

  void _editWidgetConfig(String screenName, WidgetPermissionConfig config) {
    showDialog(
      context: context,
      builder: (context) => EditPermissionDialog(
        screenName: screenName,
        widgetConfig: config,
        onSave: (updatedConfig) async {
          final service = ref.read(permissionConfigServiceProvider);
          final success = await service.updateWidgetConfig(screenName, config);
          
          if (success && mounted) {
            ref.invalidate(permissionConfigsProvider);
            AppUtils.showSuccessSnackBar(context, 'Widget configuration updated');
          } else if (mounted) {
            AppUtils.showErrorSnackBar(context, 'Failed to update configuration');
          }
        },
      ),
    );
  }

  void _toggleScreenConfig(ScreenPermissionConfig config) async {
    final service = ref.read(permissionConfigServiceProvider);
    final updatedConfig = config.copyWith(isEnabled: !config.isEnabled);
    
    final success = await service.updateScreenConfig(updatedConfig);
    
    if (success && mounted) {
      ref.invalidate(permissionConfigsProvider);
      AppUtils.showSuccessSnackBar(
        context, 
        config.isEnabled ? 'Screen disabled' : 'Screen enabled'
      );
    } else if (mounted) {
      AppUtils.showErrorSnackBar(context, 'Failed to update configuration');
    }
  }

  void _toggleWidgetConfig(String screenName, WidgetPermissionConfig config) async {
    final service = ref.read(permissionConfigServiceProvider);
    final updatedConfig = config.copyWith(isEnabled: !config.isEnabled);
    
    final success = await service.updateWidgetConfig(screenName, updatedConfig);
    
    if (success && mounted) {
      ref.invalidate(permissionConfigsProvider);
      AppUtils.showSuccessSnackBar(
        context, 
        config.isEnabled ? 'Widget disabled' : 'Widget enabled'
      );
    } else if (mounted) {
      AppUtils.showErrorSnackBar(context, 'Failed to update configuration');
    }
  }

  void _deleteScreenConfig(String screenName) {
    AppUtils.showConfirmDialog(
      context,
      title: 'Delete Configuration',
      message: 'Are you sure you want to delete the configuration for "$screenName"?',
      onConfirm: () async {
        final service = ref.read(permissionConfigServiceProvider);
        final success = await service.removeScreenConfig(screenName);
        
        if (success && mounted) {
          ref.invalidate(permissionConfigsProvider);
          AppUtils.showSuccessSnackBar(context, 'Configuration deleted');
        } else if (mounted) {
          AppUtils.showErrorSnackBar(context, 'Failed to delete configuration');
        }
      },
    );
  }

  void _exportConfigs() {
    final service = ref.read(permissionConfigServiceProvider);
    final configsJson = service.exportConfigs();
    
    // TODO: Implement file export functionality
    AppUtils.showInfoSnackBar(context, 'Export functionality coming soon');
  }

  void _importConfigs() {
    // TODO: Implement file import functionality
    AppUtils.showInfoSnackBar(context, 'Import functionality coming soon');
  }

  void _resetToDefaults() {
    AppUtils.showConfirmDialog(
      context,
      title: 'Reset to Defaults',
      message: 'Are you sure you want to reset all configurations to defaults? This action cannot be undone.',
      onConfirm: () async {
        final service = ref.read(permissionConfigServiceProvider);
        await service.resetToDefaults();
        
        if (mounted) {
          ref.invalidate(permissionConfigsProvider);
          AppUtils.showSuccessSnackBar(context, 'Configurations reset to defaults');
        }
      },
    );
  }
}
