// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(uuid())
  email        String   @unique
  username     String?  @unique
  passwordHash String   @map("password_hash")
  fullName     String   @map("full_name")
  phone        String?  @unique
  isActive     Boolean  @default(true) @map("is_active")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles                   UserRole[]
  reportedIssues              MaintenanceIssue[] @relation("ReportedBy")
  assignedIssues              MaintenanceIssue[] @relation("AssignedTo")
  resolvedIssues              MaintenanceIssue[] @relation("ResolvedBy")
  propertyMembers             PropertyMember[]
  propertyAttendance          PropertyAttendance[]
  propertyAttendanceRecorded  PropertyAttendance[] @relation("PropertyAttendanceRecorder")
  generatorFuelLogs           GeneratorFuelLog[]
  dieselAdditions             DieselAddition[]
  serviceStatusLogs           ServiceStatusLog[]
  functionProcessLogs         FunctionProcessLog[]
  escalationLogsTo            EscalationLog[] @relation("EscalatedTo")
  escalationLogsBy            EscalationLog[] @relation("EscalatedBy")
  assignedUserRoles           UserRole[] @relation("AssignedBy")

  @@map("users")
}

model Role {
  id           String   @id @default(uuid())
  name         String   @unique
  description  String?
  isSystemRole Boolean  @default(false) @map("is_system_role")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")

  // Relations
  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  resource    String
  action      String
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  rolePermissions RolePermission[]

  @@map("permissions")
}

model RolePermission {
  id           String   @id @default(uuid())
  roleId       String   @map("role_id")
  permissionId String   @map("permission_id")
  createdAt    DateTime @default(now()) @map("created_at")

  // Relations
  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole {
  id         String   @id @default(uuid())
  userId     String   @map("user_id")
  roleId     String   @map("role_id")
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy String?  @map("assigned_by")

  // Relations
  user     User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  role     Role  @relation(fields: [roleId], references: [id], onDelete: Cascade)
  assigner User? @relation("AssignedBy", fields: [assignedBy], references: [id])

  @@unique([userId, roleId])
  @@map("user_roles")
}

model Property {
  id                  String       @id @default(uuid())
  name                String
  type                PropertyType
  parentPropertyId    String?      @map("parent_property_id")
  address             String?
  description         String?

  // Office-specific fields
  capacity            Int?
  department          String?

  // Site-specific fields
  projectType         String?      @map("project_type")
  startDate           DateTime?    @map("start_date") @db.Date
  expectedEndDate     DateTime?    @map("expected_end_date") @db.Date
  hourlyRateStandard  Decimal?     @map("hourly_rate_standard") @db.Decimal(10, 2)

  // Common fields
  location            String?
  imageUrl            String?      @map("image_url")
  isActive            Boolean      @default(true) @map("is_active")
  createdAt           DateTime     @default(now()) @map("created_at")
  updatedAt           DateTime     @updatedAt @map("updated_at")

  // Relations
  parentProperty      Property?    @relation("PropertyHierarchy", fields: [parentPropertyId], references: [id])
  childProperties     Property[]   @relation("PropertyHierarchy")

  services            PropertyService[]
  maintenanceIssues   MaintenanceIssue[]
  generatorFuelLogs   GeneratorFuelLog[]
  securityGuardLogs   SecurityGuardLog[]
  dieselAdditions     DieselAddition[]
  ottServices         OttService[]
  uptimeReports       UptimeReport[]
  alerts              Alert[]
  notifications       Notification[]

  // Unified member and attendance (replaces offices/sites)
  propertyMembers     PropertyMember[]
  propertyAttendance  PropertyAttendance[]

  @@map("properties")
}

model PropertyService {
  id          String      @id @default(uuid())
  propertyId  String      @map("property_id")
  serviceType ServiceType @map("service_type")
  status      ServiceStatus
  lastChecked DateTime?   @map("last_checked")
  notes       String?
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")

  // Relations
  property          Property           @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  serviceStatusLogs ServiceStatusLog[]

  @@unique([propertyId, serviceType])
  @@map("property_services")
}

model MaintenanceIssue {
  id            String            @id @default(uuid())
  propertyId    String            @map("property_id")
  serviceType   String?           @map("service_type")
  title         String
  description   String
  priority      Priority          @default(MEDIUM)
  status        MaintenanceStatus @default(OPEN)
  department    String?
  assignedTo    String?           @map("assigned_to")
  reportedBy    String            @map("reported_by")
  resolvedBy    String?           @map("resolved_by")
  estimatedCost Decimal?          @map("estimated_cost") @db.Decimal(10, 2)
  actualCost    Decimal?          @map("actual_cost") @db.Decimal(10, 2)
  dueDate       DateTime?         @map("due_date") @db.Date
  resolvedAt    DateTime?         @map("resolved_at")
  createdAt     DateTime          @default(now()) @map("created_at")
  updatedAt     DateTime          @updatedAt @map("updated_at")

  // Relations
  property       Property        @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  reporter       User            @relation("ReportedBy", fields: [reportedBy], references: [id])
  assignee       User?           @relation("AssignedTo", fields: [assignedTo], references: [id])
  resolver       User?           @relation("ResolvedBy", fields: [resolvedBy], references: [id])
  escalationLogs EscalationLog[]

  @@map("maintenance_issues")
}

model PropertyMember {
  id         String    @id @default(uuid())
  propertyId String    @map("property_id")
  userId     String    @map("user_id")
  role       String?   // 'office_manager', 'site_supervisor', 'construction_worker', 'admin_staff', etc.
  position   String?
  department String?
  hourlyRate Decimal?  @map("hourly_rate") @db.Decimal(10, 2)
  startDate  DateTime? @map("start_date") @db.Date
  endDate    DateTime? @map("end_date") @db.Date
  isActive   Boolean   @default(true) @map("is_active")
  createdAt  DateTime  @default(now()) @map("created_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([propertyId, userId])
  @@map("property_members")
}

model PropertyAttendance {
  id           String    @id @default(uuid())
  propertyId   String    @map("property_id")
  userId       String    @map("user_id")
  date         DateTime  @db.Date
  checkInTime  String?   @map("check_in_time")
  checkOutTime String?   @map("check_out_time")
  hoursWorked  Decimal?  @map("hours_worked") @db.Decimal(4, 2)
  notes        String?
  recordedBy   String?   @map("recorded_by")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  recorder User?    @relation("PropertyAttendanceRecorder", fields: [recordedBy], references: [id])

  @@unique([propertyId, userId, date])
  @@map("property_attendance")
}

model GeneratorFuelLog {
  id                   String    @id @default(uuid())
  propertyId           String    @map("property_id")
  fuelLevelLiters      Decimal   @map("fuel_level_liters") @db.Decimal(8, 2)
  consumptionRate      Decimal?  @map("consumption_rate") @db.Decimal(6, 2)
  runtimeHours         Decimal?  @map("runtime_hours") @db.Decimal(8, 2)
  efficiencyPercentage Decimal?  @map("efficiency_percentage") @db.Decimal(5, 2)
  lastMaintenance      DateTime? @map("last_maintenance") @db.Date
  nextMaintenance      DateTime? @map("next_maintenance") @db.Date
  notes                String?
  recordedBy           String?   @map("recorded_by")
  recordedAt           DateTime  @default(now()) @map("recorded_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  recorder User?    @relation(fields: [recordedBy], references: [id])

  @@map("generator_fuel_logs")
}

model DieselAddition {
  id            String   @id @default(uuid())
  propertyId    String   @map("property_id")
  quantityLiters Decimal  @map("quantity_liters") @db.Decimal(8, 2)
  costPerLiter  Decimal? @map("cost_per_liter") @db.Decimal(6, 2)
  totalCost     Decimal? @map("total_cost") @db.Decimal(10, 2)
  supplier      String?
  receiptNumber String?  @map("receipt_number")
  addedBy       String?  @map("added_by")
  addedAt       DateTime @default(now()) @map("added_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)
  user     User?    @relation(fields: [addedBy], references: [id])

  @@map("diesel_additions")
}

model SecurityGuardLog {
  id                String   @id @default(uuid())
  propertyId        String   @map("property_id")
  guardName         String   @map("guard_name")
  shiftStart        DateTime @map("shift_start")
  shiftEnd          DateTime? @map("shift_end")
  patrolRounds      Int      @default(0) @map("patrol_rounds")
  incidentsReported Int      @default(0) @map("incidents_reported")
  visitorsLogged    Int      @default(0) @map("visitors_logged")
  notes             String?
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("security_guard_logs")
}

model OttService {
  id                String      @id @default(uuid())
  propertyId        String      @map("property_id")
  serviceName       String      @map("service_name")
  subscriptionType  String?     @map("subscription_type")
  monthlyCost       Decimal?    @map("monthly_cost") @db.Decimal(10, 2)
  renewalDate       DateTime?   @map("renewal_date") @db.Date
  status            OttStatus   @default(ACTIVE)
  loginCredentials  Json?       @map("login_credentials")
  notes             String?
  createdAt         DateTime    @default(now()) @map("created_at")
  updatedAt         DateTime    @updatedAt @map("updated_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("ott_services")
}

model UptimeReport {
  id               String   @id @default(uuid())
  propertyId       String   @map("property_id")
  serviceType      String   @map("service_type")
  date             DateTime @db.Date
  uptimePercentage Decimal? @map("uptime_percentage") @db.Decimal(5, 2)
  downtimeMinutes  Int      @default(0) @map("downtime_minutes")
  incidentsCount   Int      @default(0) @map("incidents_count")
  notes            String?
  createdAt        DateTime @default(now()) @map("created_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@unique([propertyId, serviceType, date])
  @@map("uptime_reports")
}

model ServiceStatusLog {
  id                String   @id @default(uuid())
  propertyServiceId String   @map("property_service_id")
  status            String
  message           String?
  details           Json?
  loggedBy          String?  @map("logged_by")
  loggedAt          DateTime @default(now()) @map("logged_at")

  // Relations
  propertyService PropertyService @relation(fields: [propertyServiceId], references: [id], onDelete: Cascade)
  user            User?           @relation(fields: [loggedBy], references: [id])

  @@map("service_status_logs")
}

model FunctionProcess {
  id                  String   @id @default(uuid())
  name                String
  description         String?
  category            String?
  inputParameters     Json?    @map("input_parameters")
  outputParameters    Json?    @map("output_parameters")
  executionFrequency  String?  @map("execution_frequency")
  lastExecuted        DateTime? @map("last_executed")
  status              FunctionProcessStatus @default(ACTIVE)
  createdAt           DateTime @default(now()) @map("created_at")
  updatedAt           DateTime @updatedAt @map("updated_at")

  // Relations
  functionProcessLogs FunctionProcessLog[]

  @@map("function_processes")
}

model FunctionProcessLog {
  id                  String   @id @default(uuid())
  functionProcessId   String   @map("function_process_id")
  executionTime       DateTime @default(now()) @map("execution_time")
  status              FunctionLogStatus
  inputData           Json?    @map("input_data")
  outputData          Json?    @map("output_data")
  errorMessage        String?  @map("error_message")
  executionDurationMs Int?     @map("execution_duration_ms")
  executedBy          String?  @map("executed_by")

  // Relations
  functionProcess FunctionProcess @relation(fields: [functionProcessId], references: [id], onDelete: Cascade)
  user            User?           @relation(fields: [executedBy], references: [id])

  @@map("function_process_logs")
}

model ThresholdConfig {
  id                String   @id @default(uuid())
  serviceType       String   @map("service_type")
  metricName        String   @map("metric_name")
  warningThreshold  Decimal? @map("warning_threshold") @db.Decimal(10, 2)
  criticalThreshold Decimal? @map("critical_threshold") @db.Decimal(10, 2)
  unit              String?
  description       String?
  isActive          Boolean  @default(true) @map("is_active")
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @updatedAt @map("updated_at")

  @@unique([serviceType, metricName])
  @@map("threshold_configs")
}

model Alert {
  id          String    @id @default(uuid())
  propertyId  String    @map("property_id")
  type        String
  severity    String
  title       String
  message     String
  isResolved  Boolean   @default(false) @map("is_resolved")
  resolvedAt  DateTime? @map("resolved_at")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  property Property @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("alerts")
}

model Notification {
  id          String   @id @default(uuid())
  type        String
  title       String
  message     String
  data        Json     @default("{}")
  priority    String
  propertyId  String?  @map("property_id")
  targetUsers String[] @map("target_users")
  targetRoles String[] @map("target_roles")
  isRead      Boolean  @default(false) @map("is_read")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  property Property? @relation(fields: [propertyId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

model EscalationLog {
  id                String    @id @default(uuid())
  issueId           String    @map("issue_id")
  escalationLevel   Int       @map("escalation_level")
  escalatedTo       String?   @map("escalated_to")
  escalatedBy       String?   @map("escalated_by")
  escalationReason  String?   @map("escalation_reason")
  escalatedAt       DateTime  @default(now()) @map("escalated_at")
  resolvedAt        DateTime? @map("resolved_at")
  resolutionNotes   String?   @map("resolution_notes")

  // Relations
  issue       MaintenanceIssue @relation(fields: [issueId], references: [id], onDelete: Cascade)
  escalatedToUser   User?      @relation("EscalatedTo", fields: [escalatedTo], references: [id])
  escalatedByUser   User?      @relation("EscalatedBy", fields: [escalatedBy], references: [id])

  @@map("escalation_logs")
}

// Enums
enum PropertyType {
  RESIDENTIAL       @map("residential")
  OFFICE           @map("office")
  CONSTRUCTION_SITE @map("construction_site")
}

enum ServiceType {
  ELECTRICITY @map("electricity")
  WATER       @map("water")
  INTERNET    @map("internet")
  SECURITY    @map("security")
  OTT         @map("ott")
}

enum ServiceStatus {
  OPERATIONAL @map("operational")
  WARNING     @map("warning")
  CRITICAL    @map("critical")
  MAINTENANCE @map("maintenance")
}

enum Priority {
  LOW      @map("low")
  MEDIUM   @map("medium")
  HIGH     @map("high")
  CRITICAL @map("critical")
}

enum MaintenanceStatus {
  OPEN        @map("open")
  IN_PROGRESS @map("in_progress")
  RESOLVED    @map("resolved")
  CLOSED      @map("closed")
}

enum OttStatus {
  ACTIVE   @map("active")
  INACTIVE @map("inactive")
  EXPIRED  @map("expired")
}

enum FunctionProcessStatus {
  ACTIVE      @map("active")
  INACTIVE    @map("inactive")
  MAINTENANCE @map("maintenance")
}

enum FunctionLogStatus {
  SUCCESS @map("success")
  FAILURE @map("failure")
  WARNING @map("warning")
}
