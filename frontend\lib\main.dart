import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'core/constants/app_constants.dart';
import 'core/router/app_router.dart';
import 'core/storage/storage_service.dart';
import 'core/theme/app_theme.dart';
import 'core/auth/models/user_role.dart';
import 'core/notifications/notification_service.dart';
import 'core/notifications/sse_notification_service.dart';
import 'features/auth/presentation/providers/auth_providers.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize storage
  await StorageService.init();

  // Initialize notification service
  await NotificationService().initialize();

  runApp(
    const ProviderScope(
      child: SrsrPropertyManagementApp(),
    ),
  );
}

class SrsrPropertyManagementApp extends ConsumerWidget {
  const SrsrPropertyManagementApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);

    return Consumer(
      builder: (context, ref, child) {
        final authState = ref.watch(authStateProvider);

        // Get role-based theme if user is authenticated
        ThemeData lightTheme = AppTheme.lightTheme;
        ThemeData darkTheme = AppTheme.darkTheme;

        if (authState.isAuthenticated && authState.user != null) {
          // Apply role-based theming using the user's getThemeData method
          lightTheme = authState.user!.getThemeData(context);
          // For dark theme, we'll use the same role colors but with dark base
          darkTheme = _createDarkThemeFromRole(authState.user!.getThemeData(context));
        }

        return MaterialApp.router(
          title: AppConstants.appName,
          theme: lightTheme,
          darkTheme: darkTheme,
          themeMode: ThemeMode.system,
          routerConfig: router,
          debugShowCheckedModeBanner: false,
        );
      },
    );
  }

  ThemeData _createDarkThemeFromRole(ThemeData roleTheme) {
    return AppTheme.darkTheme.copyWith(
      primaryColor: roleTheme.primaryColor,
      colorScheme: AppTheme.darkTheme.colorScheme.copyWith(
        primary: roleTheme.primaryColor,
        secondary: roleTheme.colorScheme.secondary,
      ),
      appBarTheme: AppTheme.darkTheme.appBarTheme.copyWith(
        backgroundColor: roleTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      floatingActionButtonTheme: AppTheme.darkTheme.floatingActionButtonTheme.copyWith(
        backgroundColor: roleTheme.primaryColor,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: roleTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
      ),
    );
  }
}


