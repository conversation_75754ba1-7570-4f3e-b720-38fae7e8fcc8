const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const defaultScreenPermissions = [
  {
    screenName: 'dashboard',
    requiredPermissions: [],
    allowedRoles: ['admin', 'manager', 'user'],
    isEnabled: true,
  },
  {
    screenName: 'properties',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'manager'],
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'manager', 'maintenance'],
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'manager'],
    isEnabled: true,
  },
  {
    screenName: 'fuel',
    requiredPermissions: ['view_fuel'],
    allowedRoles: ['admin', 'manager', 'fuel_operator'],
    isEnabled: true,
  },
  {
    screenName: 'admin',
    requiredPermissions: ['admin_access'],
    allowedRoles: ['admin'],
    isEnabled: true,
  },
  {
    screenName: 'reports',
    requiredPermissions: ['view_reports'],
    allowedRoles: ['admin', 'manager'],
    isEnabled: true,
  },
];

const defaultWidgetPermissions = [
  // Dashboard widgets
  {
    screenName: 'dashboard',
    widgetName: 'properties_overview',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'maintenance_summary',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'manager', 'maintenance'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'attendance_summary',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'fuel_summary',
    requiredPermissions: ['view_fuel'],
    allowedRoles: ['admin', 'manager', 'fuel_operator'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'dashboard',
    widgetName: 'alerts_summary',
    requiredPermissions: [],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  
  // Properties widgets
  {
    screenName: 'properties',
    widgetName: 'property_list',
    requiredPermissions: ['view_properties'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'add_property',
    requiredPermissions: ['create_property'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'properties',
    widgetName: 'edit_property',
    requiredPermissions: ['edit_property'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  
  // Maintenance widgets
  {
    screenName: 'maintenance',
    widgetName: 'issue_list',
    requiredPermissions: ['view_maintenance'],
    allowedRoles: ['admin', 'manager', 'maintenance'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'create_issue',
    requiredPermissions: ['create_maintenance'],
    allowedRoles: ['admin', 'manager', 'maintenance'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'maintenance',
    widgetName: 'assign_issue',
    requiredPermissions: ['assign_maintenance'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  
  // Attendance widgets
  {
    screenName: 'attendance',
    widgetName: 'attendance_list',
    requiredPermissions: ['view_attendance'],
    allowedRoles: ['admin', 'manager'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'attendance',
    widgetName: 'mark_attendance',
    requiredPermissions: ['mark_attendance'],
    allowedRoles: ['admin', 'manager', 'user'],
    isVisible: true,
    isEnabled: true,
  },
  
  // Fuel widgets
  {
    screenName: 'fuel',
    widgetName: 'fuel_logs',
    requiredPermissions: ['view_fuel'],
    allowedRoles: ['admin', 'manager', 'fuel_operator'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'fuel',
    widgetName: 'add_fuel',
    requiredPermissions: ['add_fuel'],
    allowedRoles: ['admin', 'manager', 'fuel_operator'],
    isVisible: true,
    isEnabled: true,
  },
  
  // Admin widgets
  {
    screenName: 'admin',
    widgetName: 'user_management',
    requiredPermissions: ['manage_users'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'admin',
    widgetName: 'role_management',
    requiredPermissions: ['manage_roles'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
  {
    screenName: 'admin',
    widgetName: 'permission_config',
    requiredPermissions: ['manage_permissions'],
    allowedRoles: ['admin'],
    isVisible: true,
    isEnabled: true,
  },
];

async function seedPermissions() {
  try {
    console.log('Seeding default permission configurations...');

    // Seed screen permissions
    console.log('Creating screen permissions...');
    for (const screenPermission of defaultScreenPermissions) {
      await prisma.screenPermission.upsert({
        where: { screenName: screenPermission.screenName },
        update: screenPermission,
        create: screenPermission,
      });
      console.log(`✓ Screen permission: ${screenPermission.screenName}`);
    }

    // Seed widget permissions
    console.log('Creating widget permissions...');
    for (const widgetPermission of defaultWidgetPermissions) {
      await prisma.widgetPermission.upsert({
        where: {
          screenName_widgetName: {
            screenName: widgetPermission.screenName,
            widgetName: widgetPermission.widgetName,
          },
        },
        update: widgetPermission,
        create: widgetPermission,
      });
      console.log(`✓ Widget permission: ${widgetPermission.screenName}.${widgetPermission.widgetName}`);
    }

    console.log('✅ Permission configurations seeded successfully!');
    console.log(`📊 Created ${defaultScreenPermissions.length} screen permissions`);
    console.log(`📊 Created ${defaultWidgetPermissions.length} widget permissions`);
  } catch (error) {
    console.error('❌ Error seeding permissions:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
if (require.main === module) {
  seedPermissions()
    .then(() => {
      console.log('🎉 Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { seedPermissions };
