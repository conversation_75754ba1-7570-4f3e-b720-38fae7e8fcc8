import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../../../../core/utils/app_utils.dart';
import '../providers/screen_management_providers.dart';
import '../widgets/widget_card.dart';
import '../widgets/create_widget_dialog.dart';
import '../widgets/widget_builder_dialog.dart';
import '../../data/models/custom_screen.dart';

class WidgetManagementScreen extends ConsumerStatefulWidget {
  const WidgetManagementScreen({super.key});

  @override
  ConsumerState<WidgetManagementScreen> createState() => _WidgetManagementScreenState();
}

class _WidgetManagementScreenState extends ConsumerState<WidgetManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  String _selectedType = 'all';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _searchController.addListener(() {
      setState(() {
        _searchQuery = _searchController.text;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Widget Management'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Widgets', icon: Icon(Icons.widgets)),
            Tab(text: 'Widget Types', icon: Icon(Icons.category)),
            Tab(text: 'Widget Builder', icon: Icon(Icons.build)),
          ],
        ),
        actions: [
          DynamicRoleBasedWidget(
            requiredPermissions: const ['widgets.manage'],
            child: IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                ref.invalidate(customWidgetsProvider);
                ref.invalidate(widgetTypesProvider);
              },
              tooltip: 'Refresh',
            ),
          ),
        ],
      ),
      body: DynamicRoleBasedWidget(
        requiredPermissions: const ['widgets.read'],
        fallback: _buildNoPermissionView(),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildWidgetsTab(),
            _buildWidgetTypesTab(),
            _buildWidgetBuilderTab(),
          ],
        ),
      ),
      floatingActionButton: DynamicRoleBasedFAB(
        requiredPermissions: const ['widgets.manage'],
        onPressed: () => _showCreateWidgetDialog(),
        tooltip: 'Create Widget',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildNoPermissionView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You don\'t have permission to manage widgets.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildWidgetsTab() {
    return Column(
      children: [
        _buildSearchAndFilterBar(),
        Expanded(
          child: _buildWidgetsList(),
        ),
      ],
    );
  }

  Widget _buildWidgetTypesTab() {
    final widgetTypesAsync = ref.watch(widgetTypesProvider);

    return widgetTypesAsync.when(
      data: (types) => _buildWidgetTypesList(types),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorView('Error loading widget types', error.toString()),
    );
  }

  Widget _buildWidgetBuilderTab() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Widget Builder',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'Create custom widgets using our visual builder interface.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.largePadding),
          _buildWidgetBuilderOptions(),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterBar() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search widgets...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchQuery.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchQuery = '';
                        });
                      },
                    )
                  : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Row(
            children: [
              Text(
                'Filter by type:',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: AppConstants.smallPadding),
              Expanded(
                child: DropdownButton<String>(
                  value: _selectedType,
                  isExpanded: true,
                  items: [
                    const DropdownMenuItem(value: 'all', child: Text('All Types')),
                    ...WidgetType.values.map((type) => DropdownMenuItem(
                      value: type.name,
                      child: Text(type.displayName),
                    )),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedType = value ?? 'all';
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWidgetsList() {
    final widgetsAsync = ref.watch(filteredWidgetsProvider(_searchQuery));

    return widgetsAsync.when(
      data: (widgets) {
        // Apply type filter
        final filteredWidgets = _selectedType == 'all'
            ? widgets
            : widgets.where((w) => w.type == _selectedType).toList();

        if (filteredWidgets.isEmpty) {
          return _buildEmptyState(
            'No widgets found',
            'Create your first custom widget to get started.',
            Icons.widgets,
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(customWidgetsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
            itemCount: filteredWidgets.length,
            itemBuilder: (context, index) {
              final widget = filteredWidgets[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: AppConstants.defaultPadding),
                child: WidgetCard(
                  widget: widget,
                  onEdit: () => _editWidget(widget),
                  onDelete: () => _deleteWidget(widget),
                  onToggleVisible: () => _toggleWidgetVisible(widget),
                  onDuplicate: () => _duplicateWidget(widget),
                ),
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildErrorView('Error loading widgets', error.toString()),
    );
  }

  Widget _buildWidgetTypesList(List<Map<String, dynamic>> types) {
    return ListView.builder(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      itemCount: types.length,
      itemBuilder: (context, index) {
        final type = types[index];
        return Card(
          child: ListTile(
            leading: Icon(
              _getIconForWidgetType(type['name']),
              color: Theme.of(context).primaryColor,
            ),
            title: Text(type['displayName'] ?? type['name']),
            subtitle: Text(type['description'] ?? 'No description available'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '${type['count'] ?? 0} widgets',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: () => _createWidgetOfType(type['name']),
                  tooltip: 'Create widget of this type',
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildWidgetBuilderOptions() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: AppConstants.defaultPadding,
      mainAxisSpacing: AppConstants.defaultPadding,
      childAspectRatio: 1.2,
      children: [
        _buildBuilderOptionCard(
          'Visual Builder',
          'Drag and drop interface',
          Icons.design_services,
          Colors.blue,
          () => _openVisualBuilder(),
        ),
        _buildBuilderOptionCard(
          'Code Builder',
          'Write custom widget code',
          Icons.code,
          Colors.green,
          () => _openCodeBuilder(),
        ),
        _buildBuilderOptionCard(
          'Template Builder',
          'Use pre-built templates',
          Icons.template_outlined,
          Colors.orange,
          () => _openTemplateBuilder(),
        ),
        _buildBuilderOptionCard(
          'Import Widget',
          'Import from external source',
          Icons.file_upload,
          Colors.purple,
          () => _openImportDialog(),
        ),
      ],
    );
  }

  Widget _buildBuilderOptionCard(
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 48, color: color),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String title, String subtitle, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            icon,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.largePadding),
          ElevatedButton.icon(
            onPressed: () => _showCreateWidgetDialog(),
            icon: const Icon(Icons.add),
            label: const Text('Create Widget'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(String title, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            title,
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              ref.invalidate(customWidgetsProvider);
              ref.invalidate(widgetTypesProvider);
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  IconData _getIconForWidgetType(String type) {
    switch (type) {
      case 'statCard':
        return Icons.analytics;
      case 'chart':
        return Icons.bar_chart;
      case 'table':
        return Icons.table_chart;
      case 'form':
        return Icons.form_select;
      case 'button':
        return Icons.smart_button;
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.image;
      case 'list':
        return Icons.list;
      case 'grid':
        return Icons.grid_view;
      default:
        return Icons.widgets;
    }
  }

  void _showCreateWidgetDialog() {
    showDialog(
      context: context,
      builder: (context) => const CreateWidgetDialog(),
    );
  }

  void _editWidget(CustomWidget widget) {
    showDialog(
      context: context,
      builder: (context) => CreateWidgetDialog(widget: widget),
    );
  }

  void _deleteWidget(CustomWidget widget) {
    AppUtils.showConfirmDialog(
      context,
      title: 'Delete Widget',
      message: 'Are you sure you want to delete "${widget.title}"? This action cannot be undone.',
      onConfirm: () async {
        final success = await ref.read(widgetManagementProvider.notifier).deleteWidget(widget.id);
        if (success && mounted) {
          AppUtils.showSuccessSnackBar(context, 'Widget deleted successfully');
        }
      },
    );
  }

  void _toggleWidgetVisible(CustomWidget widget) async {
    final updatedData = {
      'isVisible': !widget.isVisible,
    };
    
    final success = await ref.read(widgetManagementProvider.notifier).updateWidget(widget.id, updatedData);
    if (success && mounted) {
      AppUtils.showSuccessSnackBar(
        context, 
        widget.isVisible ? 'Widget hidden' : 'Widget shown'
      );
    }
  }

  void _duplicateWidget(CustomWidget widget) async {
    final duplicateData = {
      'name': '${widget.name}_copy',
      'title': '${widget.title} (Copy)',
      'type': widget.type,
      'properties': widget.properties,
      'requiredPermissions': widget.requiredPermissions,
      'allowedRoles': widget.allowedRoles,
      'position': widget.position,
      'styling': widget.styling,
      'isVisible': widget.isVisible,
      'order': widget.order + 1,
    };
    
    final success = await ref.read(widgetManagementProvider.notifier).createWidget(duplicateData);
    if (success && mounted) {
      AppUtils.showSuccessSnackBar(context, 'Widget duplicated successfully');
    }
  }

  void _createWidgetOfType(String type) {
    showDialog(
      context: context,
      builder: (context) => CreateWidgetDialog(preselectedType: type),
    );
  }

  void _openVisualBuilder() {
    showDialog(
      context: context,
      builder: (context) => const WidgetBuilderDialog(mode: WidgetBuilderMode.visual),
    );
  }

  void _openCodeBuilder() {
    showDialog(
      context: context,
      builder: (context) => const WidgetBuilderDialog(mode: WidgetBuilderMode.code),
    );
  }

  void _openTemplateBuilder() {
    showDialog(
      context: context,
      builder: (context) => const WidgetBuilderDialog(mode: WidgetBuilderMode.template),
    );
  }

  void _openImportDialog() {
    // TODO: Implement import dialog
    AppUtils.showInfoSnackBar(context, 'Import feature coming soon');
  }
}
