import 'package:flutter/material.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/dynamic_role_based_widget.dart';
import '../../data/models/custom_screen.dart';

class WidgetCard extends StatelessWidget {
  final CustomWidget widget;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onToggleVisible;
  final VoidCallback? onDuplicate;
  final VoidCallback? onPreview;

  const WidgetCard({
    super.key,
    required this.widget,
    this.onEdit,
    this.onDelete,
    this.onToggleVisible,
    this.onDuplicate,
    this.onPreview,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: AppConstants.smallPadding),
            _buildContent(context),
            const SizedBox(height: AppConstants.defaultPadding),
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Widget Type Icon
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: widget.isVisible ? _getTypeColor().withOpacity(0.1) : Colors.grey[200],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getWidgetTypeIcon(),
            color: widget.isVisible ? _getTypeColor() : Colors.grey[600],
            size: 24,
          ),
        ),
        const SizedBox(width: AppConstants.defaultPadding),
        
        // Widget Title and Type
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  _buildVisibilityChip(context),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: _getTypeColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      _getWidgetTypeDisplayName(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: _getTypeColor(),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Order: ${widget.order}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // Actions Menu
        _buildActionsMenu(context),
      ],
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Widget Properties Summary
        if (widget.properties.isNotEmpty) ...[
          Text(
            'Properties:',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: widget.properties.entries.take(3).map((entry) =>
              _buildPropertyChip(context, '${entry.key}: ${entry.value}')
            ).toList(),
          ),
          if (widget.properties.length > 3)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '+${widget.properties.length - 3} more properties',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[500],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          const SizedBox(height: AppConstants.smallPadding),
        ],
        
        // Permissions and Roles
        if (widget.requiredPermissions.isNotEmpty || widget.allowedRoles.isNotEmpty) ...[
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: [
              ...widget.requiredPermissions.take(2).map((permission) => 
                _buildPermissionChip(context, permission, Colors.blue)),
              ...widget.allowedRoles.take(2).map((role) => 
                _buildPermissionChip(context, role, Colors.purple)),
              if (widget.requiredPermissions.length > 2 || widget.allowedRoles.length > 2)
                _buildMoreChip(context, 
                  (widget.requiredPermissions.length - 2) + (widget.allowedRoles.length - 2)),
            ],
          ),
          const SizedBox(height: AppConstants.smallPadding),
        ],
        
        // Position and Styling Info
        Row(
          children: [
            if (widget.position != null) ...[
              Icon(Icons.place, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Positioned',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
            ],
            if (widget.styling != null) ...[
              Icon(Icons.palette, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                'Styled',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
            ],
            const Spacer(),
            Text(
              'Updated ${_formatDate(widget.updatedAt)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Row(
      children: [
        // Preview Button
        if (onPreview != null)
          TextButton.icon(
            onPressed: onPreview,
            icon: const Icon(Icons.preview, size: 16),
            label: const Text('Preview'),
          ),
        
        const Spacer(),
        
        // Action Buttons
        DynamicRoleBasedWidget(
          requiredPermissions: const ['widgets.manage'],
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Toggle Visibility Button
              if (onToggleVisible != null)
                IconButton(
                  onPressed: onToggleVisible,
                  icon: Icon(
                    widget.isVisible ? Icons.visibility_off : Icons.visibility,
                    size: 20,
                  ),
                  tooltip: widget.isVisible ? 'Hide' : 'Show',
                ),
              
              // Duplicate Button
              if (onDuplicate != null)
                IconButton(
                  onPressed: onDuplicate,
                  icon: const Icon(Icons.copy, size: 20),
                  tooltip: 'Duplicate',
                ),
              
              // Edit Button
              if (onEdit != null)
                IconButton(
                  onPressed: onEdit,
                  icon: const Icon(Icons.edit, size: 20),
                  tooltip: 'Edit',
                ),
              
              // Delete Button
              if (onDelete != null)
                IconButton(
                  onPressed: onDelete,
                  icon: const Icon(Icons.delete, size: 20, color: Colors.red),
                  tooltip: 'Delete',
                ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildVisibilityChip(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: widget.isVisible ? Colors.green[100] : Colors.grey[200],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        widget.isVisible ? 'Visible' : 'Hidden',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: widget.isVisible ? Colors.green[700] : Colors.grey[600],
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPropertyChip(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[700],
          fontSize: 10,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Widget _buildPermissionChip(BuildContext context, String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color[700],
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildMoreChip(BuildContext context, int count) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        '+$count more',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Colors.grey[600],
          fontSize: 10,
        ),
      ),
    );
  }

  Widget _buildActionsMenu(BuildContext context) {
    return DynamicRoleBasedWidget(
      requiredPermissions: const ['widgets.manage'],
      child: PopupMenuButton<String>(
        icon: const Icon(Icons.more_vert),
        onSelected: (value) {
          switch (value) {
            case 'edit':
              onEdit?.call();
              break;
            case 'duplicate':
              onDuplicate?.call();
              break;
            case 'preview':
              onPreview?.call();
              break;
            case 'export':
              // TODO: Implement export functionality
              break;
            case 'toggle':
              onToggleVisible?.call();
              break;
            case 'delete':
              onDelete?.call();
              break;
          }
        },
        itemBuilder: (context) => [
          const PopupMenuItem(
            value: 'edit',
            child: ListTile(
              leading: Icon(Icons.edit),
              title: Text('Edit'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'duplicate',
            child: ListTile(
              leading: Icon(Icons.copy),
              title: Text('Duplicate'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'preview',
            child: ListTile(
              leading: Icon(Icons.preview),
              title: Text('Preview'),
              dense: true,
            ),
          ),
          const PopupMenuItem(
            value: 'export',
            child: ListTile(
              leading: Icon(Icons.download),
              title: Text('Export'),
              dense: true,
            ),
          ),
          PopupMenuItem(
            value: 'toggle',
            child: ListTile(
              leading: Icon(widget.isVisible ? Icons.visibility_off : Icons.visibility),
              title: Text(widget.isVisible ? 'Hide' : 'Show'),
              dense: true,
            ),
          ),
          const PopupMenuDivider(),
          const PopupMenuItem(
            value: 'delete',
            child: ListTile(
              leading: Icon(Icons.delete, color: Colors.red),
              title: Text('Delete', style: TextStyle(color: Colors.red)),
              dense: true,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getWidgetTypeIcon() {
    switch (widget.type) {
      case 'statCard':
        return Icons.analytics;
      case 'chart':
        return Icons.bar_chart;
      case 'table':
        return Icons.table_chart;
      case 'form':
        return Icons.form_select;
      case 'button':
        return Icons.smart_button;
      case 'text':
        return Icons.text_fields;
      case 'image':
        return Icons.image;
      case 'list':
        return Icons.list;
      case 'grid':
        return Icons.grid_view;
      default:
        return Icons.widgets;
    }
  }

  String _getWidgetTypeDisplayName() {
    switch (widget.type) {
      case 'statCard':
        return 'Stat Card';
      case 'chart':
        return 'Chart';
      case 'table':
        return 'Table';
      case 'form':
        return 'Form';
      case 'button':
        return 'Button';
      case 'text':
        return 'Text';
      case 'image':
        return 'Image';
      case 'list':
        return 'List';
      case 'grid':
        return 'Grid';
      default:
        return 'Custom';
    }
  }

  Color _getTypeColor() {
    switch (widget.type) {
      case 'statCard':
        return Colors.blue;
      case 'chart':
        return Colors.green;
      case 'table':
        return Colors.orange;
      case 'form':
        return Colors.purple;
      case 'button':
        return Colors.red;
      case 'text':
        return Colors.teal;
      case 'image':
        return Colors.pink;
      case 'list':
        return Colors.indigo;
      case 'grid':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 7) {
      return '${date.day}/${date.month}/${date.year}';
    } else if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else {
      return 'Just now';
    }
  }
}
