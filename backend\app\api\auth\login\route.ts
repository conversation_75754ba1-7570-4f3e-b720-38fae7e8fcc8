import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { comparePassword, generateToken } from '@/lib/auth';
import { validateRequest, loginSchema, legacyLoginSchema } from '@/lib/validation';
import { createApiR<PERSON>ponse, getRequestBody, handleError, corsHeaders } from '@/lib/utils';

export async function POST(request: NextRequest) {
  try {
    const body = await getRequestBody(request);

    // Try new multi-field login first, fallback to legacy email login
    let validation = validateRequest(loginSchema, body);
    let isLegacyLogin = false;

    if (!validation.isValid) {
      // Try legacy email login format
      validation = validateRequest(legacyLoginSchema, body);
      isLegacyLogin = true;

      if (!validation.isValid) {
        return Response.json(
          createApiResponse(null, 'Validation failed', 'VALIDATION_ERROR'),
          { status: 400 }
        );
      }
    }

    let user;
    let password;

    if (isLegacyLogin) {
      // Legacy email login
      const { email, password: pwd } = validation.data;
      password = pwd;

      user = await prisma.user.findUnique({
        where: { email },
      });
    } else {
      // Multi-field login
      const { identifier, password: pwd, login_type } = validation.data;
      password = pwd;

      // Determine login type if not specified
      let detectedLoginType = login_type;
      if (!detectedLoginType) {
        if (identifier.includes('@')) {
          detectedLoginType = 'email';
        } else if (/^[+]?[1-9]\d{1,14}$/.test(identifier)) {
          detectedLoginType = 'phone';
        } else {
          detectedLoginType = 'username';
        }
      }

      // Find user based on login type
      switch (detectedLoginType) {
        case 'email':
          user = await prisma.user.findUnique({
            where: { email: identifier },
          });
          break;
        case 'username':
          user = await prisma.user.findUnique({
            where: { username: identifier },
          });
          break;
        case 'phone':
          user = await prisma.user.findUnique({
            where: { phone: identifier },
          });
          break;
        default:
          // Try all fields if type detection fails
          user = await prisma.user.findFirst({
            where: {
              OR: [
                { email: identifier },
                { username: identifier },
                { phone: identifier },
              ],
            },
          });
      }
    }

    if (!user) {
      return Response.json(
        createApiResponse(null, 'Invalid credentials', 'INVALID_CREDENTIALS'),
        { status: 401 }
      );
    }

    // Check if user is active
    if (!user.isActive) {
      return Response.json(
        createApiResponse(null, 'Account is deactivated', 'ACCOUNT_DEACTIVATED'),
        { status: 401 }
      );
    }

    // Verify password
    const isPasswordValid = await comparePassword(password, user.passwordHash);
    if (!isPasswordValid) {
      return Response.json(
        createApiResponse(null, 'Invalid credentials', 'INVALID_CREDENTIALS'),
        { status: 401 }
      );
    }

    // Get user roles
    const userRoles = await prisma.userRole.findMany({
      where: { userId: user.id },
      include: {
        role: true,
      },
    });

    const roles = userRoles.map(ur => ur.role.name);

    // Generate JWT token
    const token = generateToken({
      userId: user.id,
      email: user.email,
      roles: roles,
    });

    // Return user data without password
    const userData = {
      id: user.id,
      email: user.email,
      username: user.username,
      full_name: user.fullName,
      phone: user.phone,
      is_active: user.isActive,
      roles: roles,
      created_at: user.createdAt,
    };

    return Response.json(
      {
        success: true,
        token,
        user: userData,
      },
      {
        status: 200,
        headers: corsHeaders(),
      }
    );
  } catch (error) {
    return handleError(error, 'Login failed');
  }
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: corsHeaders(),
  });
}
