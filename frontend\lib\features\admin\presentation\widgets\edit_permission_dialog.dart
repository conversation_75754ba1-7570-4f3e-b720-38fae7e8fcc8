import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:reactive_forms/reactive_forms.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/permissions/screen_permission_config.dart';
import '../providers/role_management_providers.dart';

class EditPermissionDialog extends ConsumerStatefulWidget {
  final ScreenPermissionConfig? screenConfig;
  final String? screenName;
  final WidgetPermissionConfig? widgetConfig;
  final Function(dynamic) onSave;

  const EditPermissionDialog({
    super.key,
    this.screenConfig,
    this.screenName,
    this.widgetConfig,
    required this.onSave,
  });

  @override
  ConsumerState<EditPermissionDialog> createState() => _EditPermissionDialogState();
}

class _EditPermissionDialogState extends ConsumerState<EditPermissionDialog> {
  late FormGroup form;
  List<String> selectedPermissions = [];
  List<String> selectedRoles = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.screenConfig != null) {
      // Editing screen config
      final config = widget.screenConfig!;
      selectedPermissions = List.from(config.requiredPermissions);
      selectedRoles = List.from(config.allowedRoles);
      
      form = FormGroup({
        'name': FormControl<String>(
          value: config.screenName,
          validators: [Validators.required],
        ),
        'isEnabled': FormControl<bool>(
          value: config.isEnabled,
        ),
      });
    } else if (widget.widgetConfig != null) {
      // Editing widget config
      final config = widget.widgetConfig!;
      selectedPermissions = List.from(config.requiredPermissions);
      selectedRoles = List.from(config.allowedRoles);
      
      form = FormGroup({
        'name': FormControl<String>(
          value: config.widgetName,
          validators: [Validators.required],
        ),
        'isEnabled': FormControl<bool>(
          value: config.isEnabled,
        ),
        'isVisible': FormControl<bool>(
          value: config.isVisible,
        ),
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final rolesAsync = ref.watch(rolesProvider);
    final permissionsAsync = ref.watch(permissionsProvider);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            widget.screenConfig != null ? Icons.web : Icons.widgets,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 8),
          Text(widget.screenConfig != null ? 'Edit Screen Permissions' : 'Edit Widget Permissions'),
          const Spacer(),
          IconButton(
            icon: const Icon(Icons.close),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.8,
        height: MediaQuery.of(context).size.height * 0.7,
        child: ReactiveForm(
          formGroup: form,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Basic Information
                _buildSectionHeader('Basic Information'),
                const SizedBox(height: AppConstants.smallPadding),
                
                ReactiveTextField<String>(
                  formControlName: 'name',
                  decoration: InputDecoration(
                    labelText: widget.screenConfig != null ? 'Screen Name' : 'Widget Name',
                    prefixIcon: Icon(widget.screenConfig != null ? Icons.web : Icons.widgets),
                    border: const OutlineInputBorder(),
                  ),
                  readOnly: true, // Names shouldn't be editable
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                
                ReactiveCheckbox(
                  formControlName: 'isEnabled',
                  title: const Text('Enabled'),
                  subtitle: Text(widget.screenConfig != null 
                      ? 'Screen is enabled and accessible'
                      : 'Widget is enabled and functional'),
                ),
                
                if (widget.widgetConfig != null) ...[
                  ReactiveCheckbox(
                    formControlName: 'isVisible',
                    title: const Text('Visible'),
                    subtitle: const Text('Widget is visible to users'),
                  ),
                ],
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Permissions Section
                _buildSectionHeader('Required Permissions'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildPermissionsSection(permissionsAsync),
                
                const SizedBox(height: AppConstants.largePadding),
                
                // Roles Section
                _buildSectionHeader('Allowed Roles'),
                const SizedBox(height: AppConstants.smallPadding),
                _buildRolesSection(rolesAsync),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: isLoading ? null : _handleSave,
          child: isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.bold,
        color: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildPermissionsSection(AsyncValue<List<dynamic>> permissionsAsync) {
    return permissionsAsync.when(
      data: (permissions) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.security, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select required permissions',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: permissions.length,
                  itemBuilder: (context, index) {
                    final permission = permissions[index];
                    final permissionName = permission['name'] ?? permission.toString();
                    final isSelected = selectedPermissions.contains(permissionName);
                    
                    return CheckboxListTile(
                      dense: true,
                      title: Text(permissionName),
                      subtitle: permission is Map ? Text(permission['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedPermissions.add(permissionName);
                          } else {
                            selectedPermissions.remove(permissionName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading permissions: $error'),
    );
  }

  Widget _buildRolesSection(AsyncValue<List<dynamic>> rolesAsync) {
    return rolesAsync.when(
      data: (roles) {
        return Container(
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(AppConstants.smallPadding),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  children: [
                    const Icon(Icons.group, size: 16),
                    const SizedBox(width: 8),
                    Text(
                      'Select allowed roles',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
              ),
              Container(
                constraints: const BoxConstraints(maxHeight: 150),
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: roles.length,
                  itemBuilder: (context, index) {
                    final role = roles[index];
                    final roleName = role['name'] ?? role.toString();
                    final isSelected = selectedRoles.contains(roleName);
                    
                    return CheckboxListTile(
                      dense: true,
                      title: Text(roleName),
                      subtitle: role is Map ? Text(role['description'] ?? '') : null,
                      value: isSelected,
                      onChanged: (value) {
                        setState(() {
                          if (value == true) {
                            selectedRoles.add(roleName);
                          } else {
                            selectedRoles.remove(roleName);
                          }
                        });
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Text('Error loading roles: $error'),
    );
  }

  void _handleSave() {
    if (form.invalid) {
      form.markAllAsTouched();
      return;
    }

    setState(() {
      isLoading = true;
    });

    try {
      final formValue = form.value;

      if (widget.screenConfig != null) {
        // Save screen config
        final updatedConfig = widget.screenConfig!.copyWith(
          requiredPermissions: selectedPermissions,
          allowedRoles: selectedRoles,
          isEnabled: formValue['isEnabled'] as bool,
        );
        widget.onSave(updatedConfig);
      } else if (widget.widgetConfig != null) {
        // Save widget config
        final updatedConfig = widget.widgetConfig!.copyWith(
          requiredPermissions: selectedPermissions,
          allowedRoles: selectedRoles,
          isEnabled: formValue['isEnabled'] as bool,
          isVisible: formValue['isVisible'] as bool,
        );
        widget.onSave(updatedConfig);
      }

      Navigator.of(context).pop();
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }
}
