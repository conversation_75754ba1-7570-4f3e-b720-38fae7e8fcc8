import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/auth/widgets/permission_widget.dart';
import '../../../../core/auth/widgets/role_based_widget.dart';
import '../../../../shared/widgets/stat_card.dart';
import '../providers/user_management_providers.dart';
import '../providers/threshold_providers.dart';

class AdminDashboardScreen extends ConsumerWidget {
  const AdminDashboardScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: const RoleBasedAppBar(
        title: 'Admin Dashboard',
        showRoleIndicator: true,
      ),
      body: RoleWidget(
        allowedRoles: ['admin'],
        fallback: _buildNoPermissionView(context),
        child: RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(userManagementProvider);
            ref.invalidate(thresholdConfigProvider);
          },
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(AppConstants.defaultPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Welcome Section
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.red.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(
                            Icons.admin_panel_settings,
                            size: 32,
                            color: Colors.red,
                          ),
                        ),
                        const SizedBox(width: AppConstants.defaultPadding),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Admin Control Panel',
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                'Manage users, configure system settings, and monitor system health',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: AppConstants.largePadding),

                // User Management Section
                Text(
                  'User Management',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildUserManagementStats(context, ref),
                const SizedBox(height: AppConstants.largePadding),

                // System Configuration Section
                Text(
                  'System Configuration',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildSystemConfigCards(context),
                const SizedBox(height: AppConstants.largePadding),

                // Quick Actions Section
                Text(
                  'Quick Actions',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: AppConstants.defaultPadding),
                _buildQuickActions(context),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoPermissionView(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.lock,
            size: 64,
            color: Colors.grey,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          Text(
            'Access Denied',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          const SizedBox(height: AppConstants.smallPadding),
          Text(
            'You need admin privileges to access this area.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConstants.defaultPadding),
          ElevatedButton(
            onPressed: () => context.go('/dashboard'),
            child: const Text('Back to Dashboard'),
          ),
        ],
      ),
    );
  }

  Widget _buildUserManagementStats(BuildContext context, WidgetRef ref) {
    final usersAsync = ref.watch(userManagementProvider);

    return usersAsync.when(
      data: (users) {
        final pendingUsers = users.where((u) => !u.isApproved).length;
        final activeUsers = users.where((u) => u.isActive && u.isApproved).length;
        final inactiveUsers = users.where((u) => !u.isActive).length;

        return GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: AppConstants.smallPadding,
          mainAxisSpacing: AppConstants.smallPadding,
          childAspectRatio: 1.5,
          children: [
            StatCard(
              title: 'Total Users',
              value: users.length.toString(),
              icon: Icons.people,
              color: Colors.blue,
              onTap: () => context.push('/admin/users'),
            ),
            StatCard(
              title: 'Pending Approval',
              value: pendingUsers.toString(),
              icon: Icons.pending,
              color: Colors.orange,
              onTap: () => context.push('/admin/users'),
            ),
            StatCard(
              title: 'Active Users',
              value: activeUsers.toString(),
              icon: Icons.check_circle,
              color: Colors.green,
              onTap: () => context.push('/admin/users'),
            ),
            StatCard(
              title: 'Inactive Users',
              value: inactiveUsers.toString(),
              icon: Icons.block,
              color: Colors.red,
              onTap: () => context.push('/admin/users'),
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (_, __) => const Text('Failed to load user statistics'),
    );
  }

  Widget _buildSystemConfigCards(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: AppConstants.smallPadding,
      mainAxisSpacing: AppConstants.smallPadding,
      childAspectRatio: 1.2,
      children: [
        _buildConfigCard(
          context,
          'Threshold Settings',
          'Configure alert thresholds',
          Icons.tune,
          Colors.purple,
          () => context.push('/admin/thresholds'),
        ),
        _buildConfigCard(
          context,
          'Screen Management',
          'Create and manage custom screens',
          Icons.web,
          Colors.indigo,
          () => context.push('/admin/screens'),
        ),
        _buildConfigCard(
          context,
          'Widget Management',
          'Create and manage custom widgets',
          Icons.widgets,
          Colors.teal,
          () => context.push('/admin/widgets'),
        ),
        _buildConfigCard(
          context,
          'Role Management',
          'Manage roles and permissions',
          Icons.admin_panel_settings,
          Colors.deepOrange,
          () => context.push('/admin/roles'),
        ),
        _buildConfigCard(
          context,
          'Permission Config',
          'Configure screen and widget permissions',
          Icons.security,
          Colors.red,
          () => context.push('/admin/permissions'),
        ),
        _buildConfigCard(
          context,
          'System Settings',
          'General system configuration',
          Icons.settings,
          Colors.blue,
          () => {}, // TODO: Implement system settings
        ),
        _buildConfigCard(
          context,
          'Audit Logs',
          'View system audit logs',
          Icons.history,
          Colors.orange,
          () => {}, // TODO: Implement audit logs
        ),
      ],
    );
  }

  Widget _buildConfigCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              const SizedBox(height: AppConstants.smallPadding),
              Text(
                title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => context.push('/admin/users'),
                icon: const Icon(Icons.people),
                label: const Text('Manage Users'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => context.push('/admin/roles'),
                icon: const Icon(Icons.admin_panel_settings),
                label: const Text('Manage Roles'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => context.push('/admin/screens'),
                icon: const Icon(Icons.web),
                label: const Text('Manage Screens'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () => context.push('/admin/widgets'),
                icon: const Icon(Icons.widgets),
                label: const Text('Manage Widgets'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: AppConstants.defaultPadding),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // TODO: Implement system health check
                },
                icon: const Icon(Icons.health_and_safety),
                label: const Text('System Health'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
            const SizedBox(width: AppConstants.defaultPadding),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: () {
                  // TODO: Implement system reports
                },
                icon: const Icon(Icons.analytics),
                label: const Text('System Reports'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
